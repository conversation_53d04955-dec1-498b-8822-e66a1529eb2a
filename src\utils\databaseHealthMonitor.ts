import { Pool } from 'pg';
import { writePool, readPool, getWritePoolHealth, getReadPoolHealth, getDatabaseConfig } from '../config/database-pools';
import logger from './logger';

// Database health metrics interface
export interface DatabaseHealthMetrics {
  timestamp: Date;
  region: string;
  instanceId: string;
  writePool: {
    healthy: boolean;
    latency: number;
    utilization: number;
    totalConnections: number;
    idleConnections: number;
    waitingCount: number;
    maxConnections: number;
    consecutiveFailures: number;
    lastCheck: Date;
    error?: string;
  };
  readPool?: {
    healthy: boolean;
    latency: number;
    utilization: number;
    totalConnections: number;
    idleConnections: number;
    waitingCount: number;
    maxConnections: number;
    consecutiveFailures: number;
    lastCheck: Date;
    error?: string;
  };
  overall: {
    status: 'healthy' | 'degraded' | 'unhealthy';
    message: string;
  };
}

// Get pool metrics
function getPoolMetrics(pool: Pool, poolName: string) {
  const totalCount = pool.totalCount;
  const idleCount = pool.idleCount;
  const waitingCount = pool.waitingCount;
  const maxConnections = pool.options.max || 20;
  const utilization = Math.round((totalCount / maxConnections) * 100);

  return {
    totalConnections: totalCount,
    idleConnections: idleCount,
    waitingCount,
    maxConnections,
    utilization,
  };
}

// Check individual pool health with latency measurement
async function checkPoolHealthWithLatency(pool: Pool, poolName: string): Promise<{
  healthy: boolean;
  latency: number;
  error?: string;
}> {
  const startTime = Date.now();
  try {
    const client = await pool.connect();
    const queryStart = Date.now();
    await client.query('SELECT 1 AS health_check');
    const queryLatency = Date.now() - queryStart;
    client.release();
    
    const totalLatency = Date.now() - startTime;
    
    // Log performance warnings
    if (totalLatency > 10000) {
      logger.warn(`Slow ${poolName} pool health check: ${totalLatency}ms (query: ${queryLatency}ms)`);
    }
    
    return { healthy: true, latency: totalLatency };
  } catch (error) {
    const totalLatency = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : String(error);
    return { healthy: false, latency: totalLatency, error: errorMessage };
  }
}

// Get comprehensive database health metrics
export async function getDatabaseHealthMetrics(): Promise<DatabaseHealthMetrics> {
  const region = process.env.REGION || 'unknown';
  const instanceId = process.env.INSTANCE_ID || 'unknown';
  const dbConfig = getDatabaseConfig();
  
  // Get health status from pools
  const writeHealth = getWritePoolHealth();
  const readHealth = getReadPoolHealth();
  
  // Get real-time health checks
  const writeHealthCheck = await checkPoolHealthWithLatency(writePool, 'Write');
  const writeMetrics = getPoolMetrics(writePool, 'Write');
  
  const writePoolData = {
    healthy: writeHealthCheck.healthy,
    latency: writeHealthCheck.latency,
    utilization: writeMetrics.utilization,
    totalConnections: writeMetrics.totalConnections,
    idleConnections: writeMetrics.idleConnections,
    waitingCount: writeMetrics.waitingCount,
    maxConnections: writeMetrics.maxConnections,
    consecutiveFailures: writeHealth.consecutiveFailures,
    lastCheck: writeHealth.lastCheck,
    error: writeHealthCheck.error,
  };

  let readPoolData;
  if (dbConfig.hasReadWriteSplitting) {
    const readHealthCheck = await checkPoolHealthWithLatency(readPool, 'Read');
    const readMetrics = getPoolMetrics(readPool, 'Read');
    
    readPoolData = {
      healthy: readHealthCheck.healthy,
      latency: readHealthCheck.latency,
      utilization: readMetrics.utilization,
      totalConnections: readMetrics.totalConnections,
      idleConnections: readMetrics.idleConnections,
      waitingCount: readMetrics.waitingCount,
      maxConnections: readMetrics.maxConnections,
      consecutiveFailures: readHealth.consecutiveFailures,
      lastCheck: readHealth.lastCheck,
      error: readHealthCheck.error,
    };
  }

  // Determine overall health status
  let overallStatus: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
  let overallMessage = 'All database connections are healthy';

  if (!writePoolData.healthy) {
    overallStatus = 'unhealthy';
    overallMessage = `Write pool is unhealthy: ${writePoolData.error}`;
  } else if (readPoolData && !readPoolData.healthy) {
    overallStatus = 'degraded';
    overallMessage = `Read pool is unhealthy: ${readPoolData.error}`;
  } else if (writePoolData.utilization >= 90 || (readPoolData && readPoolData.utilization >= 90)) {
    overallStatus = 'degraded';
    overallMessage = 'High database pool utilization detected';
  } else if (writePoolData.latency > 10000 || (readPoolData && readPoolData.latency > 10000)) {
    overallStatus = 'degraded';
    overallMessage = 'High database latency detected';
  }

  return {
    timestamp: new Date(),
    region,
    instanceId,
    writePool: writePoolData,
    readPool: readPoolData,
    overall: {
      status: overallStatus,
      message: overallMessage,
    },
  };
}

// Log health metrics for monitoring
export async function logHealthMetrics(): Promise<void> {
  try {
    const metrics = await getDatabaseHealthMetrics();
    
    // Log summary
    logger.info(`Database Health Summary [${metrics.region}/${metrics.instanceId}]: ${metrics.overall.status.toUpperCase()} - ${metrics.overall.message}`);
    
    // Log detailed metrics
    logger.info(`Write Pool: ${metrics.writePool.healthy ? 'HEALTHY' : 'UNHEALTHY'} | Latency: ${metrics.writePool.latency}ms | Utilization: ${metrics.writePool.utilization}% (${metrics.writePool.totalConnections}/${metrics.writePool.maxConnections})`);
    
    if (metrics.readPool) {
      logger.info(`Read Pool: ${metrics.readPool.healthy ? 'HEALTHY' : 'UNHEALTHY'} | Latency: ${metrics.readPool.latency}ms | Utilization: ${metrics.readPool.utilization}% (${metrics.readPool.totalConnections}/${metrics.readPool.maxConnections})`);
    }
    
    // Log warnings for concerning metrics
    if (metrics.writePool.utilization >= 80) {
      logger.warn(`Write pool high utilization: ${metrics.writePool.utilization}% - consider scaling`);
    }
    
    if (metrics.writePool.latency > 5000) {
      logger.warn(`Write pool high latency: ${metrics.writePool.latency}ms - network issues possible`);
    }
    
    if (metrics.writePool.consecutiveFailures > 0) {
      logger.warn(`Write pool has ${metrics.writePool.consecutiveFailures} consecutive failures`);
    }
    
  } catch (error) {
    logger.error('Failed to log health metrics:', error instanceof Error ? error : new Error(String(error)));
  }
}

// DISABLED: Start periodic health logging - REPLACED WITH CONSOLIDATED HEALTH CHECK
// This function has been disabled to prevent connection pool exhaustion from
// multiple overlapping health checks. Use consolidatedHealthChecker instead.
export function startHealthMonitoring(intervalMs?: number): void {
  const region = process.env.REGION || 'unknown';

  logger.warn(`[HEALTH MONITOR] DISABLED - Database health monitoring disabled to prevent connection leaks`);
  logger.warn(`[HEALTH MONITOR] Use consolidatedHealthChecker for health monitoring instead`);
  logger.info(`[HEALTH MONITOR] Region: ${region} - monitoring consolidated to prevent pool exhaustion`);

  // Do not start any health monitoring - this is now handled by consolidatedHealthChecker
}
