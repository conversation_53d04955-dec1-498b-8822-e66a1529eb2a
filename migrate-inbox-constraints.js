#!/usr/bin/env node

/**
 * Migration script to fix TempFly.io inbox data consistency issue
 * 
 * This script:
 * 1. Drops the existing unique constraint on address
 * 2. Creates a partial unique index for active inboxes only
 * 3. Creates maintenance_logs table
 * 4. Sets up cleanup functions
 * 
 * Usage: node migrate-inbox-constraints.js
 */

const { Pool } = require('pg');
require('dotenv').config();

// Database configuration
const pool = new Pool({
  host: process.env.PGHOST || 'localhost',
  port: parseInt(process.env.PGPORT || '5432'),
  database: process.env.PGDATABASE || 'tempfly_app',
  user: process.env.PGUSER || 'postgres',
  password: process.env.PGPASSWORD,
  ssl: process.env.PGSSLMODE === 'disable' ? false : { rejectUnauthorized: false },
});

const migrations = [
  {
    name: 'Drop existing unique constraint',
    sql: `ALTER TABLE inboxes DROP CONSTRAINT IF EXISTS inboxes_address_key;`
  },
  {
    name: 'Create partial unique index for active inboxes',
    sql: `CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_inboxes_address_active_unique 
          ON inboxes (address) 
          WHERE is_active = true;`
  },
  {
    name: 'Create maintenance_logs table',
    sql: `CREATE TABLE IF NOT EXISTS maintenance_logs (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            operation VARCHAR(255) NOT NULL,
            details TEXT,
            affected_rows INTEGER DEFAULT 0,
            execution_time INTEGER DEFAULT 0,
            status VARCHAR(50) DEFAULT 'success',
            error_message TEXT,
            executed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
          );`
  },
  {
    name: 'Create indexes for maintenance_logs',
    sql: `CREATE INDEX IF NOT EXISTS idx_maintenance_logs_operation ON maintenance_logs(operation);
          CREATE INDEX IF NOT EXISTS idx_maintenance_logs_executed_at ON maintenance_logs(executed_at);`
  },
  {
    name: 'Create cleanup function',
    sql: `CREATE OR REPLACE FUNCTION cleanup_expired_inboxes()
          RETURNS INTEGER AS $$
          DECLARE
              affected_count INTEGER;
              start_time TIMESTAMP;
              execution_time INTEGER;
          BEGIN
              start_time := clock_timestamp();
              
              UPDATE inboxes
              SET is_active = false
              WHERE expiry_date IS NOT NULL
              AND expiry_date < NOW()
              AND is_active = true;
              
              GET DIAGNOSTICS affected_count = ROW_COUNT;
              execution_time := EXTRACT(EPOCH FROM (clock_timestamp() - start_time)) * 1000;
              
              INSERT INTO maintenance_logs (
                  operation,
                  details,
                  affected_rows,
                  execution_time,
                  status
              ) VALUES (
                  'cleanup_expired_inboxes_cron',
                  'Automated cleanup of expired inboxes',
                  affected_count,
                  execution_time,
                  'success'
              );
              
              RETURN affected_count;
          EXCEPTION
              WHEN OTHERS THEN
                  INSERT INTO maintenance_logs (
                      operation,
                      details,
                      affected_rows,
                      execution_time,
                      status,
                      error_message
                  ) VALUES (
                      'cleanup_expired_inboxes_cron',
                      'Automated cleanup of expired inboxes - FAILED',
                      0,
                      EXTRACT(EPOCH FROM (clock_timestamp() - start_time)) * 1000,
                      'error',
                      SQLERRM
                  );
                  
                  RAISE;
          END;
          $$ LANGUAGE plpgsql;`
  }
];

async function runMigration() {
  const client = await pool.connect();
  
  try {
    console.log('🚀 Starting TempFly.io inbox constraint migration...');
    
    // Check current state
    console.log('\n📊 Checking current database state...');
    
    const constraintCheck = await client.query(`
      SELECT conname, contype 
      FROM pg_constraint 
      WHERE conrelid = 'inboxes'::regclass 
      AND conname LIKE '%address%'
    `);
    
    console.log('Current constraints:', constraintCheck.rows);
    
    const duplicateCheck = await client.query(`
      SELECT address, COUNT(*) as count
      FROM inboxes 
      WHERE is_active = true
      GROUP BY address 
      HAVING COUNT(*) > 1
    `);
    
    if (duplicateCheck.rows.length > 0) {
      console.log('⚠️  Warning: Found active duplicate addresses:', duplicateCheck.rows);
      console.log('Please resolve these duplicates before proceeding.');
      return;
    }
    
    // Run migrations
    for (const migration of migrations) {
      console.log(`\n🔧 Running: ${migration.name}`);
      try {
        await client.query(migration.sql);
        console.log(`✅ Success: ${migration.name}`);
      } catch (error) {
        console.error(`❌ Failed: ${migration.name}`, error.message);
        throw error;
      }
    }
    
    // Verify the changes
    console.log('\n🔍 Verifying changes...');
    
    const indexCheck = await client.query(`
      SELECT indexname, indexdef 
      FROM pg_indexes 
      WHERE tablename = 'inboxes' 
      AND indexname LIKE '%address%'
    `);
    
    console.log('New indexes:', indexCheck.rows);
    
    const tableCheck = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_name = 'maintenance_logs'
    `);
    
    console.log('Maintenance logs table exists:', tableCheck.rows.length > 0);
    
    // Test the cleanup function
    console.log('\n🧪 Testing cleanup function...');
    const cleanupTest = await client.query('SELECT cleanup_expired_inboxes() as cleaned_count');
    console.log(`Cleanup test result: ${cleanupTest.rows[0].cleaned_count} expired inboxes processed`);
    
    console.log('\n🎉 Migration completed successfully!');
    console.log('\n📝 Next steps:');
    console.log('1. Deploy the updated application code');
    console.log('2. Set up pg_cron for automated cleanup (requires superuser)');
    console.log('3. Monitor the maintenance_logs table for cleanup operations');
    
  } catch (error) {
    console.error('\n💥 Migration failed:', error);
    throw error;
  } finally {
    client.release();
  }
}

async function main() {
  try {
    await runMigration();
  } catch (error) {
    console.error('Migration error:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

if (require.main === module) {
  main();
}

module.exports = { runMigration };
