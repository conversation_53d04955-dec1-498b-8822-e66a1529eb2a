import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { InboxModel, Inbox } from '../models/Inbox';
import { DomainModel } from '../models/Domain';
import { AppError } from '../middlewares/errorHandler';
import { formatDate, formatTime } from '../utils/dateFormatter';
import { deleteCache, isRedisConnected } from '../config/redis-direct';
import logger from '../utils/logger';
import { generateRandomInboxName } from '../utils/randomGenerator';
import { FREE_USER_INBOX_LIMIT } from '../config/limits';
import { memoryCache } from '../middlewares/cacheMiddleware';
import { runInBackground } from '../utils/backgroundTasks';

/**
 * Enhanced helper function to invalidate inbox list cache for a specific user
 * This ensures that when an inbox is created or deleted, the list is updated
 * and all related caches are properly invalidated
 */
const invalidateInboxListCache = async (apiKeyId?: string, rapidApiKey?: string): Promise<void> => {
  try {
    // Create the cache key pattern for the user's inbox list
    const userKey = rapidApiKey || apiKeyId || 'anonymous';

    // PHASE 2: Precise cache invalidation strategy to reduce overhead
    const cachePatterns = [
      // User-specific inbox list caches (most important)
      `inboxes:list:${userKey}`,            // Exact user inbox list cache

      // PHASE 2: Reduced wildcard patterns to minimize cache scanning overhead
      `cache:GET:/api/inboxes`,             // Basic inbox list cache

      // Only invalidate memory cache for this specific user to reduce overhead
      `inboxes:count:${userKey}`            // User-specific inbox count cache
    ];

    // Invalidate all cache patterns in parallel
    await Promise.all(cachePatterns.map(pattern => deleteCache(pattern)));

    // Also clear the memory cache for any entries related to inbox lists
    const memoryKeys = Object.keys(memoryCache);
    const inboxListKeys = memoryKeys.filter(key =>
      key.includes('inboxes:list') ||
      key.includes('/api/inboxes') ||
      key.includes('inbox:') ||
      key.includes('inboxes:')
    );

    if (inboxListKeys.length > 0) {
      if (process.env.NODE_ENV !== 'production') {
        logger.debug(`Clearing ${inboxListKeys.length} related entries from memory cache`);
      }
      inboxListKeys.forEach(key => {
        delete memoryCache[key];
      });
    }

    // Force clear the entire memory cache if in development mode
    if (process.env.NODE_ENV !== 'production' && process.env.FORCE_CLEAR_MEMORY_CACHE === 'true') {
      logger.debug('Forcing complete memory cache clear');
      Object.keys(memoryCache).forEach(key => {
        delete memoryCache[key];
      });
    }

    // Log the cache invalidation
    if (process.env.NODE_ENV !== 'production') {
      logger.debug(`Invalidated inbox list cache for user: ${userKey}`);
    }
  } catch (error: unknown) {
    // Log the error but don't throw it
    // Convert unknown error to a string or Error object for proper logging
    if (error instanceof Error) {
      logger.error('Error invalidating inbox list cache:', error);
    } else {
      logger.error('Error invalidating inbox list cache:', new Error(String(error)));
    }
  }
};

// Cache key for inbox counts
const getInboxCountCacheKey = (rapidApiKey: string) => `inbox:count:${rapidApiKey}`;



/**
 * Create a new inbox
 */
export const createInbox = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    if (process.env.NODE_ENV !== 'production') {
      logger.debug('Creating inbox with request body:', req.body);
    }
    // Make sure req.body exists
    if (!req.body) {
      return next(new AppError('Request body is missing', 400));
    }

    // Safely access properties with defaults (case-insensitive)
    let name = req.body.name || req.body.Name || '';
    const domain = req.body.domain || req.body.Domain;
    const lifespan = req.body.lifespan || req.body.Lifespan;

    // Require lifespan parameter
    if (lifespan === undefined || lifespan === null) {
      return next(new AppError('Lifespan parameter is required', 400));
    }

    // Generate random name if not provided or empty
    if (!name || name.trim() === '') {
      name = generateRandomInboxName();
      logger.info(`Generated random inbox name: ${name}`);
    } else {
      // Validate name format (only letters, numbers, underscores, and hyphens)
      const nameRegex = /^[a-zA-Z0-9_-]+$/;
      if (!nameRegex.test(name)) {
        return next(
          new AppError(
            'Name must contain only letters, numbers, underscores, and hyphens',
            400
          )
        );
      }

      // Check for restricted usernames (common system or administrative names)
      const restrictedUsernames = [
        'admin', 'administrator', 'support', 'help', 'contact', 'info', 'no-reply', 'noreply',
        'postmaster', 'webmaster', 'hostmaster', 'abuse', 'security', 'billing', 'sales', 'service',
        'mail', 'email', 'system', 'root', 'staff', 'team', 'official', 'account', 'feedback'
      ];

      // Case-insensitive check
      if (restrictedUsernames.some(restricted => name.toLowerCase() === restricted.toLowerCase())) {
        // Instead of returning a specific error about restricted usernames,
        // return the same error as if the inbox already exists
        logger.info(`Attempt to use restricted username: ${name}`);
        return next(new AppError(`Inbox with email ${name}@${domain || 'domain'} already exists`, 400));
      }
    }

    // Get domain (either from request or random)
    let inboxDomain = domain;
    if (!inboxDomain) {
      inboxDomain = await DomainModel.getRandomDomain();
      if (!inboxDomain) {
        return next(new AppError('No available domains found', 500));
      }
    } else {
      // Validate domain if provided
      const isValidDomain = await DomainModel.isValidDomain(inboxDomain);
      if (!isValidDomain) {
        return next(new AppError('Invalid domain', 400));
      }
    }

    // Calculate expiration date
    // If lifespan is provided, use it; otherwise, default to 30 days
    const expiresAt = new Date();

    // Define valid lifespan values in seconds
    const validLifespans = [
      0,        // Long-time use (1 year)
      300,      // 5 minutes
      600,      // 10 minutes
      900,      // 15 minutes
      1200,     // 20 minutes
      1800,     // 30 minutes
      3600,     // 1 hour
      10800,    // 3 hours
      21600,    // 6 hours
      32400,    // 9 hours
      43200,    // 12 hours
      86400,    // 1 day
      259200,   // 3 days
      604800,   // 1 week
      1296000,  // 15 days
      2592000,  // 30 days
      7776000,  // 90 days
      15552000, // 180 days
      23328000, // 270 days
    ];

    // Always log lifespan for debugging
    logger.info(`Creating inbox with lifespan: ${lifespan} (type: ${typeof lifespan})`);

    // Convert lifespan to number if it's a string
    const lifespanNumber = typeof lifespan === 'string' ? parseInt(lifespan, 10) : lifespan;

    logger.info(`Converted lifespan: ${lifespanNumber} (type: ${typeof lifespanNumber})`);

    // Validate lifespan if provided
    if (!validLifespans.includes(lifespanNumber)) {
      logger.warn(`Invalid lifespan value: ${lifespanNumber}`);
      return next(new AppError('Invalid lifespan value', 400));
    }

    if (lifespanNumber === 0) {
      // For long-time use (lifespan=0), set expiration to 1 year
      expiresAt.setFullYear(expiresAt.getFullYear() + 1);
      logger.info(`Setting expiry to 1 year from now: ${expiresAt.toISOString()}`);
    } else {
      // Use provided lifespan (in seconds)
      expiresAt.setSeconds(expiresAt.getSeconds() + lifespanNumber);
      logger.info(`Setting expiry based on lifespan (${lifespanNumber} seconds): ${expiresAt.toISOString()}`);
    }

    // Get API key ID if available
    const apiKeyId = req.apiKey?.id || null;

    // Get RapidAPI key if available
    const rapidApiKey = (req as any).rapidApi?.key || null;

    // Add timeout protection to inbox creation with increased timeout
    let inbox: Inbox;
    try {
      // Create a promise for the inbox creation with a longer timeout
      const createPromise = InboxModel.create(name, inboxDomain, expiresAt, apiKeyId, rapidApiKey);
      const timeoutPromise = new Promise<Inbox>((_, reject) => {
        setTimeout(() => reject(new AppError('Inbox creation timeout', 408)), 15000); // Increased to 15 seconds
      });

      // Race the creation against the timeout
      inbox = await Promise.race([createPromise, timeoutPromise]);

      // Log the ownership information for debugging
      if (process.env.NODE_ENV !== 'production') {
        logger.debug(`Created inbox with apiKeyId: ${apiKeyId}, rapidApiKey: ${rapidApiKey}`);
      }

      // OPTIMIZED POST-CREATION VALIDATION: Move to background for better performance
      // This is a safety measure to catch any race conditions that might have occurred
      if ((req as any).rapidApi?.usage?.planType === 'FREE' && rapidApiKey) {
        // Run post-creation validation in background to avoid blocking the response
        runInBackground(`post-creation-validation-${inbox.id}`, async () => {
          try {
            const postCreationStartTime = Date.now();
            const postCreationCount = await InboxModel.countActiveInboxesByRapidApiKey(rapidApiKey);
            const validationTime = Date.now() - postCreationStartTime;

            logger.info(`Post-creation validation completed in ${validationTime}ms: ${postCreationCount}/${FREE_USER_INBOX_LIMIT} inboxes for ${rapidApiKey}`);

            if (postCreationCount > FREE_USER_INBOX_LIMIT) {
              logger.error(`🚨 CRITICAL: Post-creation limit violation detected! User ${(req as any).rapidApi.user} now has ${postCreationCount} inboxes (limit: ${FREE_USER_INBOX_LIMIT})`);
              logger.error(`🚨 Race condition detected - inbox ${inbox.id} will be deactivated`);

              // Deactivate the just-created inbox to enforce the limit
              await InboxModel.deactivate(inbox.id, rapidApiKey);

              // Log the enforcement action
              logger.warn(`🔧 LIMIT ENFORCEMENT: Deactivated inbox ${inbox.id} due to post-creation limit violation`);

              // Note: We can't return an error to the user here since the response was already sent
              // The user will see the inbox was created, but it will be deactivated shortly after
              // This is acceptable since it's a rare race condition scenario
            } else {
              logger.debug(`✅ Post-creation validation passed: ${postCreationCount}/${FREE_USER_INBOX_LIMIT} inboxes for ${rapidApiKey}`);
            }

          } catch (validationError) {
            logger.error('Post-creation validation failed:', validationError instanceof Error ? validationError : new Error(String(validationError)));
            // Continue - the inbox was created successfully and the validation is just a safety net
          }
        });
      }

    } catch (error) {
      logger.error('Error creating inbox:', error instanceof Error ? error : new Error(String(error)));
      return next(error);
    }

    // Move ALL cache invalidation to background without awaiting
    // This significantly improves response time
    runInBackground(`invalidate-cache-after-inbox-creation-${inbox.id}`, async () => {
      try {
        // First, invalidate the most critical cache - inbox list
        await invalidateInboxListCache(apiKeyId, rapidApiKey);

        if (process.env.NODE_ENV !== 'production') {
          logger.debug(`Invalidated inbox list cache for user in background`);
        }

        // Only continue with additional cache invalidation if Redis is connected
        if (isRedisConnected()) {
          // Invalidate inbox count cache for this user
          if (rapidApiKey) {
            const countCacheKey = getInboxCountCacheKey(rapidApiKey);
            await deleteCache(countCacheKey);

            if (process.env.NODE_ENV !== 'production') {
              logger.debug(`Invalidated inbox count cache for user: ${rapidApiKey}`);
            }
          }

          // Invalidate any other caches that might be affected by the new inbox
          // This is a safety measure to ensure all caches are eventually consistent
          const email = `${name}@${inboxDomain}`;
          const additionalPatterns = [
            `inbox:info:${email}`,              // Inbox info cache
            `cache:GET:/api/inboxes/${inbox.id}*`, // Any cached requests for this specific inbox
            `inboxes:*`,                        // Any cached inbox lists
            `cache:GET:/api/inboxes*`           // Any cached inbox API requests
          ];

          // Invalidate additional cache patterns in parallel
          await Promise.all(additionalPatterns.map(pattern => deleteCache(pattern)));

          if (process.env.NODE_ENV !== 'production') {
            logger.debug(`Completed background cache invalidation for new inbox: ${inbox.id}`);
          }
        }
      } catch (cacheError) {
        // Log but don't fail if cache invalidation fails
        logger.error('Background cache invalidation failed:',
          cacheError instanceof Error ? cacheError : new Error(String(cacheError)));
      }
    });

    // Get request ID from middleware or generate new one
    const requestId = (req as any).requestId || uuidv4().toUpperCase();

    res.status(201).json({
      'request-id': requestId,
      'message': 'Success',
      data: {
        inbox: {
          id: inbox.id,
          email: inbox.email,
          name: inbox.name,
          domain: inbox.domain,
          created_date: formatDate(inbox.created_at),
          created_time: formatTime(inbox.created_at),
          expiry_date: formatDate(inbox.expires_at),
          expiry_time: formatTime(inbox.expires_at),
        },
      },
    });
  } catch (error: any) {
    // Log the error for debugging
    logger.error('Error creating inbox:', error);
    if (error.stack) {
      logger.debug('Error stack:', error.stack);
    }
    if (error.code) {
      logger.debug('Error code:', error.code);
    }
    if (error.detail) {
      logger.debug('Error detail:', error.detail);
    }

    next(error);
  }
};

/**
 * List all inboxes
 */
export const listInboxes = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Get pagination parameters from validated query or defaults
    // These should already be validated by the middleware
    const validatedQuery = (req as any).validatedQuery || {};
    const page = validatedQuery.page || 1;
    const size = validatedQuery.size || 10;

    // Get name filter if provided
    let name: string | undefined = undefined;
    if (validatedQuery.name) {
      name = validatedQuery.name.trim();
      if (name === '') {
        name = undefined;
      }
    } else if (req.query.name && typeof req.query.name === 'string') {
      // Fallback to req.query if validatedQuery is not available
      name = req.query.name.trim();
      if (name === '') {
        name = undefined;
      }
    }

    // Validate pagination parameters (should already be validated by middleware, but double-check)
    if (page < 1 || size < 1 || size > 100) {
      return next(
        new AppError(
          'Invalid pagination parameters. Page must be >= 1 and size must be between 1 and 100',
          400
        )
      );
    }

    // Get API key ID if available
    const apiKeyId = req.apiKey?.id;

    // Get RapidAPI key if the request comes from RapidAPI
    // Only use RapidAPI key for filtering if the request actually came through RapidAPI
    const rapidApiKey = (req as any).rapidApi ? (req as any).rapidApi.key : undefined;

    // Log authentication info for debugging
    if (process.env.NODE_ENV !== 'production') {
      logger.debug(`Listing inboxes with apiKeyId: ${apiKeyId || 'none'}, rapidApiKey: ${rapidApiKey || 'none'}, isRapidApiRequest: ${!!(req as any).rapidApi}`);
    }

    // List inboxes, filtering by API key or RapidAPI key if available
    const { inboxes, total } = await InboxModel.list(page, size, name, apiKeyId, rapidApiKey);

    // Get request ID from middleware or generate new one
    const requestId = (req as any).requestId || uuidv4().toUpperCase();

    res.status(200).json({
      'request-id': requestId,
      'message': 'Success',
      data: {
        inboxes: inboxes.map((inbox) => ({
          id: inbox.id,
          email: inbox.email,
          name: inbox.name,
          domain: inbox.domain,
          created_date: formatDate(inbox.created_at),
          created_time: formatTime(inbox.created_at),
          expiry_date: formatDate(inbox.expires_at),
          expiry_time: formatTime(inbox.expires_at),
        })),
        pagination: {
          page,
          size,
          total,
          pages: Math.ceil(total / size),
        },
      },
    });
  } catch (error: any) {
    // ENHANCED ERROR HANDLING: Handle all possible database constraint violations
    if (error.code === '23505') {
      // Handle different constraint names that can cause duplicate key violations
      if (error.constraint === 'idx_inboxes_address_active_unique' ||
          error.constraint === 'inboxes_address_key' ||
          error.constraint === 'inboxes_address_unique' ||
          error.constraint === 'inboxes_email_key') {

        // Extract the email from the error message with multiple patterns
        let emailAddress = 'this inbox';

        // Try different regex patterns to extract the email
        const patterns = [
          /\(address\)=\((.+?)\)/i,  // (address)=(<EMAIL>)
          /\(email\)=\((.+?)\)/i,   // (email)=(<EMAIL>)
          /Key \(address\)=\((.+?)\)/i, // Key (address)=(<EMAIL>)
          /Key \(email\)=\((.+?)\)/i    // Key (email)=(<EMAIL>)
        ];

        for (const pattern of patterns) {
          const match = error.detail?.match(pattern);
          if (match && match[1]) {
            emailAddress = match[1];
            break;
          }
        }

        return next(new AppError(`Inbox with address ${emailAddress} already exists. Please choose a different username.`, 400));
      }

      // Handle other potential unique constraints
      if (error.constraint && error.constraint.includes('address')) {
        return next(new AppError(`Inbox address already exists. Please choose a different username.`, 400));
      }
    }

    // If the error is already an AppError, just pass it along
    if (error instanceof AppError) {
      return next(error);
    }

    // Log the error for debugging with more context
    const errorDetails: any = {
      message: error.message,
      stack: error.stack
    };

    // Add database-specific properties if they exist
    if ('code' in error) errorDetails.code = (error as any).code;
    if ('constraint' in error) errorDetails.constraint = (error as any).constraint;
    if ('detail' in error) errorDetails.detail = (error as any).detail;

    logger.error('Error creating inbox:', errorDetails);

    // Pass other errors to the global error handler
    next(error);
  }
};

/**
 * Delete an inbox
 */
export const deleteInbox = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Safely access id parameter
    const id = req.params && req.params.id ? req.params.id : '';

    if (!id) {
      return next(new AppError('Inbox ID is required', 400));
    }

    const inbox = await InboxModel.getById(id);
    if (!inbox) {
      return next(new AppError('Inbox not found', 404));
    }

    // Get API key ID if available
    const apiKeyId = req.apiKey?.id;

    // Get RapidAPI key if the request comes from RapidAPI
    // Only use RapidAPI key for ownership verification if the request actually came through RapidAPI
    const rapidApiKey = (req as any).rapidApi ? (req as any).rapidApi.key : undefined;

    // Log authentication info for debugging
    if (process.env.NODE_ENV !== 'production') {
      logger.debug(`Deleting inbox with apiKeyId: ${apiKeyId || 'none'}, rapidApiKey: ${rapidApiKey || 'none'}, isRapidApiRequest: ${!!(req as any).rapidApi}`);
    }

    // Check ownership based on API key or RapidAPI key
    let hasOwnership = false;

    // Check API key ownership if available
    if (apiKeyId && inbox.api_key_id) {
      hasOwnership = inbox.api_key_id === apiKeyId;
    }

    // Check RapidAPI key ownership if available
    if (!hasOwnership && rapidApiKey && inbox.rapidapi_key) {
      hasOwnership = inbox.rapidapi_key === rapidApiKey;
    }

    // If the inbox has an owner and the current user is not the owner, deny access
    if ((inbox.api_key_id || inbox.rapidapi_key) && !hasOwnership) {
      return next(new AppError('You do not have permission to delete this inbox', 403));
    }

    // Get the inbox email before deleting it
    const inboxEmail = inbox.email;

    // Get user identification before deleting the inbox
    const userApiKeyId = req.apiKey?.id || null;
    const userRapidApiKey = (req as any).rapidApi?.key || null;
    const inboxId = id;
    const email = inboxEmail;

    // IMMEDIATE CACHE INVALIDATION: Invalidate inbox list cache before deleting
    // This ensures the user doesn't see the deleted inbox in subsequent list requests
    await invalidateInboxListCache(userApiKeyId, userRapidApiKey);

    // Delete the inbox
    await InboxModel.delete(id);

    // Immediately invalidate specific inbox caches to ensure consistency
    if (isRedisConnected()) {
      // These are critical caches that need immediate invalidation
      const criticalCachePatterns = [
        `inbox:info:${email}`,                  // Inbox info cache
        `cache:GET:/api/inboxes/${inboxId}`,    // Direct inbox access
        `email:${inboxId}:*`                    // Any cached individual emails for this inbox
      ];

      // Invalidate critical cache patterns in parallel
      await Promise.all(criticalCachePatterns.map(pattern => deleteCache(pattern)));

      // Clear memory cache for this specific inbox
      const memoryKeys = Object.keys(memoryCache);
      const criticalMemoryKeys = memoryKeys.filter(key =>
        key.includes(inboxId) ||
        key.includes(email)
      );

      if (criticalMemoryKeys.length > 0) {
        if (process.env.NODE_ENV !== 'production') {
          logger.debug(`Clearing ${criticalMemoryKeys.length} critical memory cache entries for inbox: ${inboxId}`);
        }
        criticalMemoryKeys.forEach(key => {
          delete memoryCache[key];
        });
      }

      // Use the background task utility for more comprehensive cache invalidation
      runInBackground(`invalidate-cache-after-inbox-deletion-${inboxId}`, async () => {
        // Create an array of additional cache patterns to invalidate
        const additionalCachePatterns = [
          // Email-related caches
          `emails:inbox:${inboxId}:*`,          // Email list cache for this inbox
          `cache:GET:/api/inboxes/${inboxId}/messages*`, // Any cached email list or detail requests

          // Any other related caches
          `cache:*${inboxId}*`,                 // Any other caches containing the inbox ID
          `cache:*${email}*`                    // Any other caches containing the email
        ];

        // Invalidate additional cache patterns in parallel
        await Promise.all(additionalCachePatterns.map(pattern => deleteCache(pattern)));

        // Also clear any remaining memory cache entries that might be related
        const remainingMemoryKeys = Object.keys(memoryCache).filter(key =>
          key.includes('/api/inboxes') &&
          !criticalMemoryKeys.includes(key)
        );

        if (remainingMemoryKeys.length > 0) {
          if (process.env.NODE_ENV !== 'production') {
            logger.debug(`Clearing ${remainingMemoryKeys.length} additional memory cache entries`);
          }
          remainingMemoryKeys.forEach(key => {
            delete memoryCache[key];
          });
        }

        // Invalidate inbox count cache if this is a RapidAPI user
        if (userRapidApiKey) {
          const countCacheKey = getInboxCountCacheKey(userRapidApiKey);
          await deleteCache(countCacheKey);

          if (process.env.NODE_ENV !== 'production') {
            logger.debug(`Invalidated inbox count cache for user: ${userRapidApiKey}`);
          }
        }

        if (process.env.NODE_ENV !== 'production') {
          logger.debug(`Completed background cache invalidation for deleted inbox: ${inboxId}`);
        }
      });
    }

    // Get request ID from middleware or generate new one
    const requestId = (req as any).requestId || uuidv4().toUpperCase();

    res.status(200).json({
      'request-id': requestId,
      'message': 'Inbox deleted successfully',
      data: null
    });
  } catch (error) {
    next(error);
  }
};
