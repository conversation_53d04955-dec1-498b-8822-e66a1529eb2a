/**
 * Consolidated Health Check System
 * 
 * This module consolidates all health check operations into a single
 * coordinated system to prevent connection pool exhaustion from
 * multiple overlapping health checks.
 */

import { Pool, PoolClient } from 'pg';
import { writePool, readPool } from '../config/database-pools';
import logger from './logger';

interface HealthCheckResult {
  healthy: boolean;
  latency: number;
  error?: string;
  timestamp: Date;
}

interface ConsolidatedHealthMetrics {
  writePool: HealthCheckResult;
  readPool?: HealthCheckResult;
  poolMetrics: {
    writePool: {
      totalConnections: number;
      idleConnections: number;
      waitingClients: number;
      maxConnections: number;
      utilization: number;
    };
    readPool?: {
      totalConnections: number;
      idleConnections: number;
      waitingClients: number;
      maxConnections: number;
      utilization: number;
    };
  };
  lastUpdate: Date;
}

class ConsolidatedHealthChecker {
  private lastHealthCheck: ConsolidatedHealthMetrics | null = null;
  private isChecking = false;
  private checkInterval: NodeJS.Timeout | null = null;
  private readonly region: string;
  private readonly isIndiaRegion: boolean;
  private readonly checkIntervalMs: number;

  constructor() {
    this.region = process.env.REGION || 'unknown';
    this.isIndiaRegion = this.region.toLowerCase().includes('india') || 
                         this.region.toLowerCase().includes('in');
    
    // Consolidated check interval - single check for all health monitoring needs
    this.checkIntervalMs = this.isIndiaRegion ? 180000 : 120000; // 3 minutes for India, 2 minutes for others
    
    logger.info(`[CONSOLIDATED HEALTH] Initialized for region ${this.region} (India: ${this.isIndiaRegion}) - interval: ${this.checkIntervalMs}ms`);
  }

  private async checkPoolHealth(pool: Pool, poolName: string): Promise<HealthCheckResult> {
    const startTime = Date.now();
    let client: PoolClient | null = null;
    
    try {
      // Add timeout protection
      const connectPromise = pool.connect();
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Health check timeout')), 15000);
      });
      
      client = await Promise.race([connectPromise, timeoutPromise]);
      
      const queryStart = Date.now();
      await client.query('SELECT 1 AS consolidated_health_check');
      const queryLatency = Date.now() - queryStart;
      
      const totalLatency = Date.now() - startTime;
      
      return {
        healthy: true,
        latency: totalLatency,
        timestamp: new Date(),
      };
      
    } catch (error) {
      const totalLatency = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      return {
        healthy: false,
        latency: totalLatency,
        error: errorMessage,
        timestamp: new Date(),
      };
      
    } finally {
      if (client) {
        try {
          client.release();
        } catch (releaseError) {
          logger.error(`[CONSOLIDATED HEALTH] Failed to release ${poolName} client:`, releaseError instanceof Error ? releaseError : new Error(String(releaseError)));
        }
      }
    }
  }

  private getPoolMetrics(pool: Pool, poolName: string) {
    return {
      totalConnections: pool.totalCount,
      idleConnections: pool.idleCount,
      waitingClients: pool.waitingCount,
      maxConnections: pool.options.max || 20,
      utilization: Math.round((pool.totalCount / (pool.options.max || 20)) * 100),
    };
  }

  private async performConsolidatedCheck(): Promise<ConsolidatedHealthMetrics> {
    if (this.isChecking) {
      // If already checking, return cached result or wait
      if (this.lastHealthCheck) {
        return this.lastHealthCheck;
      }
      throw new Error('Health check already in progress');
    }

    this.isChecking = true;
    
    try {
      logger.debug('[CONSOLIDATED HEALTH] Starting consolidated health check');
      
      // Check write pool
      const writeHealthCheck = await this.checkPoolHealth(writePool, 'Write');
      const writeMetrics = this.getPoolMetrics(writePool, 'Write');
      
      // Check read pool if it's separate
      let readHealthCheck: HealthCheckResult | undefined;
      let readMetrics: any;
      
      if (readPool !== writePool) {
        readHealthCheck = await this.checkPoolHealth(readPool, 'Read');
        readMetrics = this.getPoolMetrics(readPool, 'Read');
      }
      
      const result: ConsolidatedHealthMetrics = {
        writePool: writeHealthCheck,
        readPool: readHealthCheck,
        poolMetrics: {
          writePool: writeMetrics,
          readPool: readMetrics,
        },
        lastUpdate: new Date(),
      };
      
      this.lastHealthCheck = result;
      
      // Log health status
      this.logHealthStatus(result);
      
      return result;
      
    } finally {
      this.isChecking = false;
    }
  }

  private logHealthStatus(metrics: ConsolidatedHealthMetrics): void {
    const { writePool, readPool, poolMetrics } = metrics;
    
    // Log write pool status
    const writeStatus = writePool.healthy ? 'HEALTHY' : 'UNHEALTHY';
    const writeUtil = poolMetrics.writePool.utilization;
    logger.info(`[CONSOLIDATED HEALTH] Write Pool: ${writeStatus} | Latency: ${writePool.latency}ms | Utilization: ${writeUtil}% (${poolMetrics.writePool.totalConnections}/${poolMetrics.writePool.maxConnections})`);
    
    // Log read pool status if separate
    if (readPool && poolMetrics.readPool) {
      const readStatus = readPool.healthy ? 'HEALTHY' : 'UNHEALTHY';
      const readUtil = poolMetrics.readPool.utilization;
      logger.info(`[CONSOLIDATED HEALTH] Read Pool: ${readStatus} | Latency: ${readPool.latency}ms | Utilization: ${readUtil}% (${poolMetrics.readPool.totalConnections}/${poolMetrics.readPool.maxConnections})`);
    }
    
    // Log warnings for high utilization
    if (writeUtil >= 80) {
      logger.warn(`[CONSOLIDATED HEALTH] Write pool high utilization: ${writeUtil}% - consider scaling`);
    }
    
    if (writePool.latency > 5000) {
      logger.warn(`[CONSOLIDATED HEALTH] Write pool high latency: ${writePool.latency}ms - network issues possible`);
    }
    
    // India-specific warnings
    if (this.isIndiaRegion) {
      if (writeUtil >= 70) {
        logger.warn(`[INDIA] Pool utilization at ${writeUtil}% - monitoring for potential issues`);
      }
      
      if (poolMetrics.writePool.waitingClients > 0) {
        logger.warn(`[INDIA] ${poolMetrics.writePool.waitingClients} clients waiting for connections`);
      }
    }
  }

  public async getHealthMetrics(useCache = true): Promise<ConsolidatedHealthMetrics> {
    // Return cached result if recent and cache is allowed
    if (useCache && this.lastHealthCheck) {
      const age = Date.now() - this.lastHealthCheck.lastUpdate.getTime();
      const maxAge = this.checkIntervalMs / 2; // Use cache for half the check interval
      
      if (age < maxAge) {
        return this.lastHealthCheck;
      }
    }
    
    // Perform new check
    return await this.performConsolidatedCheck();
  }

  public getLastHealthCheck(): ConsolidatedHealthMetrics | null {
    return this.lastHealthCheck;
  }

  public start(): void {
    if (this.checkInterval) {
      logger.warn('[CONSOLIDATED HEALTH] Already running, stopping previous instance');
      this.stop();
    }

    logger.info(`[CONSOLIDATED HEALTH] Starting consolidated health monitoring every ${this.checkIntervalMs}ms`);
    
    // Initial check after a delay
    setTimeout(() => {
      this.performConsolidatedCheck().catch(error => {
        logger.error('[CONSOLIDATED HEALTH] Initial health check failed:', error);
      });
    }, 10000); // 10 second delay
    
    // Regular checks
    this.checkInterval = setInterval(() => {
      this.performConsolidatedCheck().catch(error => {
        logger.error('[CONSOLIDATED HEALTH] Scheduled health check failed:', error);
      });
    }, this.checkIntervalMs);
  }

  public stop(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
      logger.info('[CONSOLIDATED HEALTH] Stopped health monitoring');
    }
  }

  public isHealthy(): boolean {
    if (!this.lastHealthCheck) return false;
    
    const writeHealthy = this.lastHealthCheck.writePool.healthy;
    const readHealthy = this.lastHealthCheck.readPool?.healthy ?? true;
    
    return writeHealthy && readHealthy;
  }

  public getUtilization(): { write: number; read?: number } {
    if (!this.lastHealthCheck) return { write: 0 };
    
    return {
      write: this.lastHealthCheck.poolMetrics.writePool.utilization,
      read: this.lastHealthCheck.poolMetrics.readPool?.utilization,
    };
  }
}

// Create singleton instance
export const consolidatedHealthChecker = new ConsolidatedHealthChecker();

// Auto-start the consolidated health checker
setTimeout(() => {
  consolidatedHealthChecker.start();
  logger.info('[CONSOLIDATED HEALTH] Auto-started consolidated health monitoring');
}, 5000); // Start after 5 seconds

// Cleanup on process exit
process.on('SIGTERM', () => consolidatedHealthChecker.stop());
process.on('SIGINT', () => consolidatedHealthChecker.stop());

export default consolidatedHealthChecker;
