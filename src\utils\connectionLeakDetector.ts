/**
 * Connection Leak Detection Utility
 * 
 * This module helps detect and prevent database connection leaks,
 * especially critical for the India region which has limited connection pools.
 */

import { Pool } from 'pg';
import logger from './logger';
import { writePool, readPool } from '../config/database-pools';

interface ConnectionLeakMetrics {
  timestamp: Date;
  poolName: string;
  totalConnections: number;
  maxConnections: number;
  idleConnections: number;
  waitingClients: number;
  utilizationPercent: number;
}

interface LeakDetectionConfig {
  highUtilizationThreshold: number;
  criticalUtilizationThreshold: number;
  monitoringInterval: number;
  alertCooldown: number;
  maxHistorySize: number;
}

class ConnectionLeakDetector {
  private config: LeakDetectionConfig;
  private metrics: ConnectionLeakMetrics[] = [];
  private lastAlertTime: Map<string, number> = new Map();
  private monitoringInterval: NodeJS.Timeout | null = null;
  private isIndiaRegion: boolean;

  constructor() {
    const region = process.env.REGION || 'unknown';
    const instanceId = process.env.INSTANCE_ID || 'unknown';
    
    this.isIndiaRegion = region.toLowerCase().includes('india') || 
                         region.toLowerCase().includes('in') ||
                         instanceId.toLowerCase().includes('india');

    // Configure thresholds based on region
    this.config = {
      highUtilizationThreshold: this.isIndiaRegion ? 70 : 80,
      criticalUtilizationThreshold: this.isIndiaRegion ? 85 : 90,
      monitoringInterval: this.isIndiaRegion ? 10000 : 15000, // More frequent for India
      alertCooldown: 60000, // 1 minute between similar alerts
      maxHistorySize: 100,
    };

    logger.info(`Connection leak detector initialized for ${region} (India region: ${this.isIndiaRegion})`);
  }

  private getPoolMetrics(pool: Pool, poolName: string): ConnectionLeakMetrics {
    const totalConnections = pool.totalCount;
    const idleConnections = pool.idleCount;
    const waitingClients = pool.waitingCount;
    const maxConnections = pool.options.max || 20;
    const utilizationPercent = Math.round((totalConnections / maxConnections) * 100);

    return {
      timestamp: new Date(),
      poolName,
      totalConnections,
      maxConnections,
      idleConnections,
      waitingClients,
      utilizationPercent,
    };
  }

  private shouldAlert(alertKey: string): boolean {
    const lastAlert = this.lastAlertTime.get(alertKey) || 0;
    const now = Date.now();
    
    if (now - lastAlert > this.config.alertCooldown) {
      this.lastAlertTime.set(alertKey, now);
      return true;
    }
    
    return false;
  }

  private analyzeMetrics(metrics: ConnectionLeakMetrics): void {
    const { poolName, utilizationPercent, totalConnections, maxConnections, waitingClients } = metrics;
    
    // Critical utilization alert
    if (utilizationPercent >= this.config.criticalUtilizationThreshold) {
      const alertKey = `critical-${poolName}`;
      if (this.shouldAlert(alertKey)) {
        logger.error(`[LEAK DETECTOR] CRITICAL: ${poolName} pool at ${utilizationPercent}% utilization (${totalConnections}/${maxConnections}) - potential connection leak detected!`);
        
        if (waitingClients > 0) {
          logger.error(`[LEAK DETECTOR] ${waitingClients} clients waiting for connections - immediate action required`);
        }
        
        // Log recent history for debugging
        this.logRecentHistory(poolName);
      }
    }
    // High utilization warning
    else if (utilizationPercent >= this.config.highUtilizationThreshold) {
      const alertKey = `high-${poolName}`;
      if (this.shouldAlert(alertKey)) {
        logger.warn(`[LEAK DETECTOR] HIGH: ${poolName} pool at ${utilizationPercent}% utilization (${totalConnections}/${maxConnections})`);
      }
    }

    // Detect potential leak patterns
    this.detectLeakPatterns(poolName);
  }

  private detectLeakPatterns(poolName: string): void {
    const recentMetrics = this.metrics
      .filter(m => m.poolName === poolName)
      .slice(-10); // Last 10 measurements

    if (recentMetrics.length < 5) return;

    // Check for consistently high utilization
    const highUtilizationCount = recentMetrics.filter(
      m => m.utilizationPercent >= this.config.highUtilizationThreshold
    ).length;

    if (highUtilizationCount >= 5) {
      const alertKey = `pattern-${poolName}`;
      if (this.shouldAlert(alertKey)) {
        logger.warn(`[LEAK DETECTOR] PATTERN: ${poolName} pool showing consistent high utilization - possible slow leak`);
      }
    }

    // Check for growing connection count without corresponding idle connections
    const latest = recentMetrics[recentMetrics.length - 1];
    const earliest = recentMetrics[0];
    
    if (latest.totalConnections > earliest.totalConnections && 
        latest.idleConnections <= earliest.idleConnections) {
      const alertKey = `growth-${poolName}`;
      if (this.shouldAlert(alertKey)) {
        logger.warn(`[LEAK DETECTOR] GROWTH: ${poolName} pool connections growing without idle increase - investigate active connections`);
      }
    }
  }

  private logRecentHistory(poolName: string): void {
    const recentMetrics = this.metrics
      .filter(m => m.poolName === poolName)
      .slice(-5); // Last 5 measurements

    logger.info(`[LEAK DETECTOR] Recent ${poolName} pool history:`);
    recentMetrics.forEach((metric, index) => {
      const timeAgo = Math.round((Date.now() - metric.timestamp.getTime()) / 1000);
      logger.info(`  ${index + 1}. ${timeAgo}s ago: ${metric.utilizationPercent}% (${metric.totalConnections}/${metric.maxConnections}, idle: ${metric.idleConnections}, waiting: ${metric.waitingClients})`);
    });
  }

  private monitor(): void {
    try {
      // Monitor write pool
      const writeMetrics = this.getPoolMetrics(writePool, 'Write');
      this.metrics.push(writeMetrics);
      this.analyzeMetrics(writeMetrics);

      // Monitor read pool if it's separate
      if (readPool !== writePool) {
        const readMetrics = this.getPoolMetrics(readPool, 'Read');
        this.metrics.push(readMetrics);
        this.analyzeMetrics(readMetrics);
      }

      // Trim history to prevent memory growth
      if (this.metrics.length > this.config.maxHistorySize) {
        this.metrics = this.metrics.slice(-this.config.maxHistorySize);
      }

    } catch (error) {
      logger.error('[LEAK DETECTOR] Error during monitoring:', error instanceof Error ? error : new Error(String(error)));
    }
  }

  public start(): void {
    if (this.monitoringInterval) {
      logger.warn('[LEAK DETECTOR] Already running, stopping previous instance');
      this.stop();
    }

    logger.info(`[LEAK DETECTOR] Starting monitoring every ${this.config.monitoringInterval}ms`);
    this.monitoringInterval = setInterval(() => this.monitor(), this.config.monitoringInterval);

    // Initial monitoring
    setTimeout(() => this.monitor(), 5000); // Start after 5 seconds
  }

  public stop(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
      logger.info('[LEAK DETECTOR] Monitoring stopped');
    }
  }

  public getMetrics(): ConnectionLeakMetrics[] {
    return [...this.metrics];
  }

  public getCurrentStatus(): { write: ConnectionLeakMetrics; read?: ConnectionLeakMetrics } {
    const writeMetrics = this.getPoolMetrics(writePool, 'Write');
    const result: { write: ConnectionLeakMetrics; read?: ConnectionLeakMetrics } = { write: writeMetrics };
    
    if (readPool !== writePool) {
      result.read = this.getPoolMetrics(readPool, 'Read');
    }
    
    return result;
  }
}

// Create singleton instance
export const connectionLeakDetector = new ConnectionLeakDetector();

// Auto-start for India region
const region = process.env.REGION || 'unknown';
const isIndiaRegion = region.toLowerCase().includes('india') || region.toLowerCase().includes('in');

if (isIndiaRegion) {
  // Start leak detection automatically for India region
  setTimeout(() => {
    connectionLeakDetector.start();
    logger.info('[LEAK DETECTOR] Auto-started for India region');
  }, 10000); // Start after 10 seconds to allow pool initialization
}

// Cleanup on process exit
process.on('SIGTERM', () => connectionLeakDetector.stop());
process.on('SIGINT', () => connectionLeakDetector.stop());

export default connectionLeakDetector;
